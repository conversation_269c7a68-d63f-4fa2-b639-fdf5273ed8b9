#!/bin/bash

# Bug召回API服务启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
check_python() {
    print_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装，请先安装Python3"
        exit 1
    fi
    
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    print_success "Python版本: $python_version"
}

# 检查依赖包
check_dependencies() {
    print_info "检查依赖包..."
    
    # 检查requirements.txt是否存在
    if [ ! -f "requirements.txt" ]; then
        print_warning "requirements.txt 文件不存在"
    else
        print_info "安装依赖包..."
        pip3 install -r requirements.txt
    fi
    
    # 检查关键依赖
    python3 -c "import flask" 2>/dev/null || {
        print_error "Flask 未安装，请运行: pip install flask"
        exit 1
    }
    
    python3 -c "import pandas" 2>/dev/null || {
        print_error "pandas 未安装，请运行: pip install pandas"
        exit 1
    }
    
    print_success "依赖包检查完成"
}

# 检查环境变量
check_env_vars() {
    print_info "检查环境变量..."
    
    # 检查.env文件
    if [ -f ".env" ]; then
        print_info "发现.env文件，加载环境变量"
        export $(cat .env | grep -v '^#' | xargs)
    fi
    
    # 检查必要的环境变量
    if [ -z "$BUG_NAMESPACE" ]; then
        print_warning "BUG_NAMESPACE 环境变量未设置"
        print_info "请设置Bug知识库命名空间: export BUG_NAMESPACE=your_namespace"
    else
        print_success "Bug命名空间: $BUG_NAMESPACE"
    fi
    
    if [ -z "$BUG_COLLECTION" ]; then
        print_warning "BUG_COLLECTION 环境变量未设置"
        print_info "请设置Bug知识库集合: export BUG_COLLECTION=your_collection"
    else
        print_success "Bug集合: $BUG_COLLECTION"
    fi
    
    # 设置默认API配置
    export BUG_RECALL_API_HOST=${BUG_RECALL_API_HOST:-"0.0.0.0"}
    export BUG_RECALL_API_PORT=${BUG_RECALL_API_PORT:-"5001"}
    export BUG_RECALL_API_DEBUG=${BUG_RECALL_API_DEBUG:-"false"}
    
    print_success "API服务配置: http://$BUG_RECALL_API_HOST:$BUG_RECALL_API_PORT"
}

# 检查端口是否被占用
check_port() {
    local port=${BUG_RECALL_API_PORT:-5001}
    print_info "检查端口 $port 是否可用..."
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_error "端口 $port 已被占用"
        print_info "请停止占用端口的进程或更改端口配置"
        print_info "查看占用进程: lsof -i :$port"
        exit 1
    fi
    
    print_success "端口 $port 可用"
}

# 创建必要的目录
create_directories() {
    print_info "创建必要的目录..."
    
    # 创建数据目录
    mkdir -p data/bug_recall
    mkdir -p data/log
    mkdir -p docs
    
    print_success "目录创建完成"
}

# 启动服务
start_service() {
    print_info "启动Bug召回API服务..."
    
    # 检查服务文件是否存在
    if [ ! -f "bug_recall_api.py" ]; then
        print_error "bug_recall_api.py 文件不存在"
        exit 1
    fi
    
    print_success "正在启动服务..."
    print_info "服务地址: http://$BUG_RECALL_API_HOST:$BUG_RECALL_API_PORT"
    print_info "健康检查: http://$BUG_RECALL_API_HOST:$BUG_RECALL_API_PORT/api/v1/bug_recall/health"
    print_info "API文档: docs/bug_recall_api.md"
    print_info ""
    print_info "按 Ctrl+C 停止服务"
    print_info "=" * 60
    
    # 启动Python服务
    python3 bug_recall_api.py
}

# 显示帮助信息
show_help() {
    echo "Bug召回API服务启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -t, --test     运行测试"
    echo "  -c, --check    仅检查环境，不启动服务"
    echo ""
    echo "环境变量:"
    echo "  BUG_NAMESPACE           Bug知识库命名空间"
    echo "  BUG_COLLECTION          Bug知识库集合"
    echo "  BUG_RECALL_API_HOST     API服务主机 (默认: 0.0.0.0)"
    echo "  BUG_RECALL_API_PORT     API服务端口 (默认: 5001)"
    echo "  BUG_RECALL_API_DEBUG    调试模式 (默认: false)"
    echo ""
    echo "示例:"
    echo "  $0                      启动服务"
    echo "  $0 --test              运行测试"
    echo "  $0 --check             检查环境"
    echo ""
}

# 运行测试
run_test() {
    print_info "运行Bug召回API测试..."
    
    if [ ! -f "test_bug_recall_api.py" ]; then
        print_error "test_bug_recall_api.py 文件不存在"
        exit 1
    fi
    
    # 等待服务启动
    local host=${BUG_RECALL_API_HOST:-"localhost"}
    local port=${BUG_RECALL_API_PORT:-5001}
    local base_url="http://$host:$port"
    
    print_info "等待服务启动..."
    sleep 3
    
    # 运行测试
    python3 test_bug_recall_api.py "$base_url"
}

# 主函数
main() {
    echo "=" * 60
    echo "Bug召回API服务启动脚本"
    echo "=" * 60
    
    # 解析命令行参数
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -t|--test)
            check_python
            check_dependencies
            check_env_vars
            create_directories
            run_test
            exit 0
            ;;
        -c|--check)
            check_python
            check_dependencies
            check_env_vars
            check_port
            create_directories
            print_success "环境检查完成，可以启动服务"
            exit 0
            ;;
        "")
            # 默认启动服务
            check_python
            check_dependencies
            check_env_vars
            check_port
            create_directories
            start_service
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap 'print_info "服务已停止"; exit 0' INT TERM

# 运行主函数
main "$@"
