import json
import os
from fastapi import HTTPException
from flask import Flask, Response, request, jsonify
import time
from openai import OpenAI
from rag_server.recall_case import ReCallCase
from tools.recall_case_tool import create_response, extract_https_links
from tools.bug_recall import BugRecallTool
from utils.logger.logger import Logging
from dotenv import load_dotenv

logger = Logging().get_logger()
hy_client = OpenAI(base_url=os.getenv('hunyuan_url'), api_key=os.getenv('hunyuan_t1_key'))
load_dotenv()
app = Flask(__name__)

# 初始化Bug召回工具
bug_recall_tool = BugRecallTool()
@app.post('/medical_case_recall')
def sse_stream():
    try:
        body = request.json
        story_url = body.get("story_url")
        story_content = body.get("story_content")
    except Exception:
        raise HTTPException(400, "story_url参数必填")
    logger.info(f"测试堂传递的需求描述 需求url:{story_url} ,需求信息:{story_content},")
    recall_client = ReCallCase(story_url=story_url)
    def generate():
        module_id = str(time.time())
        # 构造模块信息并推送
        module_params = {
            "data": [
                {
                    "id": module_id,
                    "content": "回归用例",
                    "parent_id": "",
                    "node_type": "FEATURE"
                }
            ]
        }
        module_params_str = json.dumps(module_params, ensure_ascii=False)
        yield f"data: {module_params_str}\n\n"
        for core in recall_client.cores:
            case = recall_client.gen_regress_case(core, is_zhiyan=True, module_id=module_id).get("result", [])
            if len(case) == 0:
                continue
            return_data = json.dumps({"data": case},ensure_ascii=False)
            # 构造 SSE 格式的数据块
            data = f"data: {return_data}\n\n"
            yield data
    # 设置响应头
    return Response(
        generate(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # 禁用缓冲（关键）
        }
    )
@app.post('/medical_case_recall_evalution')
def recall_case_for_evalution():
    try:
        body = request.json
        story_url = body.get("story_url")
    except Exception:
        raise HTTPException(400, "story_url参数必填")

    https_links = extract_https_links(story_url)
    if len(https_links) <= 0:
        return create_response(code=0, msg="", data="入参中需要包含需求单链接信息")

    story_url = https_links[0]

    recall_client = ReCallCase(story_url=story_url)
    case_dict = {"result": [], "rag_doc": [], "rerank_doc": [], "query":[]}
    for core in recall_client.cores:
        dict = recall_client.gen_regress_case(core)
        case_dict["result"].extend(dict["result"])
        case_dict["rag_doc"].extend(dict["rag_doc"])
        case_dict["rerank_doc"].extend(dict["rerank_doc"])
        case_dict["query"].extend(dict["query"])

    return create_response(code=0, msg="", data=case_dict)


@app.route('/bug_recall', methods=['POST'])
def bug_recall():
    """
    Bug召回接口
    根据需求链接召回相关Bug并生成测试用例建议
    """
    try:
        # 获取请求参数
        body = request.json
        if not body:
            return jsonify({
                "code": 400,
                "msg": "请求体不能为空",
                "data": None
            }), 400

        story_url = body.get("story_url")
        if not story_url:
            return jsonify({
                "code": 400,
                "msg": "story_url参数必填",
                "data": None
            }), 400

        logger.info(f"开始Bug召回，需求URL: {story_url}")

        # 调用Bug召回工具
        result = bug_recall_tool.recall_bugs_by_story_url(story_url)

        if result is None:
            return jsonify({
                "code": 500,
                "msg": "Bug召回失败，请检查需求链接或Bug知识库配置",
                "data": None
            }), 500

        # 构建响应数据
        response_data = {
            "story_info": result.get("story_info", {}),
            "relevant_bugs": result.get("relevant_bugs", []),
            "suggested_test_cases": result.get("suggested_test_cases", []),
            "search_stats": result.get("search_stats", {}),
            "file_path": result.get("file_path", "")
        }

        logger.info(f"Bug召回完成，找到 {len(response_data['relevant_bugs'])} 个相关Bug，生成 {len(response_data['suggested_test_cases'])} 个测试用例")

        return jsonify({
            "code": 0,
            "msg": "Bug召回成功",
            "data": response_data
        })

    except Exception as e:
        logger.error(f"Bug召回接口异常: {str(e)}")
        return jsonify({
            "code": 500,
            "msg": f"服务器内部错误: {str(e)}",
            "data": None
        }), 500


@app.route('/bug_recall/search', methods=['POST'])
def bug_search():
    """
    Bug搜索接口
    仅搜索相关Bug，不生成测试用例
    """
    try:
        # 获取请求参数
        body = request.json
        if not body:
            return jsonify({
                "code": 400,
                "msg": "请求体不能为空",
                "data": None
            }), 400

        story_url = body.get("story_url")
        if not story_url:
            return jsonify({
                "code": 400,
                "msg": "story_url参数必填",
                "data": None
            }), 400

        logger.info(f"开始Bug搜索，需求URL: {story_url}")

        # 获取需求信息
        from utils.tapd import TAPDUtils
        tapd_utils = TAPDUtils()
        story = tapd_utils.get_story(story_url)
        if not story:
            return jsonify({
                "code": 400,
                "msg": "无法获取需求信息，请检查链接是否正确",
                "data": None
            }), 400

        # 从TRAG向量数据库搜索相关Bug
        bugs = bug_recall_tool.search_bugs_from_trag(story)

        # 分析bug相关性
        relevant_bugs = bug_recall_tool.analyze_bugs_relevance(story, bugs)

        response_data = {
            "story_info": {
                "name": story.get('name', ''),
                "description": story.get('description', ''),
                "category": story.get('category_name', ''),
                "search_method": "TRAG向量搜索"
            },
            "relevant_bugs": relevant_bugs,
            "search_stats": {
                "total_searched": len(bugs),
                "trag_filtered": len([b for b in bugs if b.get('trag_relevance_score', 0) > 0]),
                "llm_filtered": len(relevant_bugs),
                "final_count": len(relevant_bugs)
            }
        }

        logger.info(f"Bug搜索完成，找到 {len(relevant_bugs)} 个相关Bug")

        return jsonify({
            "code": 0,
            "msg": "Bug搜索成功",
            "data": response_data
        })

    except Exception as e:
        logger.error(f"Bug搜索接口异常: {str(e)}")
        return jsonify({
            "code": 500,
            "msg": f"服务器内部错误: {str(e)}",
            "data": None
        }), 500


@app.route('/bug_recall/test_cases', methods=['POST'])
def generate_bug_test_cases():
    """
    基于Bug生成测试用例接口
    """
    try:
        # 获取请求参数
        body = request.json
        if not body:
            return jsonify({
                "code": 400,
                "msg": "请求体不能为空",
                "data": None
            }), 400

        story_url = body.get("story_url")
        bugs = body.get("bugs", [])

        if not story_url:
            return jsonify({
                "code": 400,
                "msg": "story_url参数必填",
                "data": None
            }), 400

        if not bugs:
            return jsonify({
                "code": 400,
                "msg": "bugs参数必填",
                "data": None
            }), 400

        logger.info(f"开始生成Bug测试用例，需求URL: {story_url}, Bug数量: {len(bugs)}")

        # 获取需求信息
        from utils.tapd import TAPDUtils
        tapd_utils = TAPDUtils()
        story = tapd_utils.get_story(story_url)
        if not story:
            return jsonify({
                "code": 400,
                "msg": "无法获取需求信息，请检查链接是否正确",
                "data": None
            }), 400

        # 生成测试用例
        test_cases = bug_recall_tool.generate_bug_test_cases(story, bugs)

        response_data = {
            "story_info": {
                "name": story.get('name', ''),
                "description": story.get('description', ''),
                "category": story.get('category_name', '')
            },
            "test_cases": test_cases,
            "stats": {
                "input_bugs": len(bugs),
                "generated_cases": len(test_cases)
            }
        }

        logger.info(f"Bug测试用例生成完成，生成 {len(test_cases)} 个测试用例")

        return jsonify({
            "code": 0,
            "msg": "测试用例生成成功",
            "data": response_data
        })

    except Exception as e:
        logger.error(f"Bug测试用例生成接口异常: {str(e)}")
        return jsonify({
            "code": 500,
            "msg": f"服务器内部错误: {str(e)}",
            "data": None
        }), 500


@app.route('/bug_recall/health', methods=['GET'])
def health_check():
    """
    健康检查接口
    """
    try:
        # 检查Bug知识库连接状态
        trag_status = "connected" if bug_recall_tool.trag_manager else "disconnected"

        return jsonify({
            "code": 0,
            "msg": "服务正常",
            "data": {
                "service": "bug_recall_api",
                "status": "healthy",
                "trag_connection": trag_status,
                "bug_namespace": bug_recall_tool.bug_namespace,
                "bug_collection": bug_recall_tool.bug_collection
            }
        })

    except Exception as e:
        logger.error(f"健康检查异常: {str(e)}")
        return jsonify({
            "code": 500,
            "msg": f"服务异常: {str(e)}",
            "data": None
        }), 500


if __name__ == '__main__':
    app.run(debug=True, threaded=True, host='0.0.0.0', port=5000)
