version: '3.8'

services:
  bug-recall-api:
    build:
      context: .
      dockerfile: Dockerfile.bug_recall_api
    container_name: bug-recall-api
    ports:
      - "5001:5001"
    environment:
      # Bug知识库配置
      - BUG_NAMESPACE=${BUG_NAMESPACE}
      - BUG_COLLECTION=${BUG_COLLECTION}
      
      # API服务配置
      - BUG_RECALL_API_HOST=0.0.0.0
      - BUG_RECALL_API_PORT=5001
      - BUG_RECALL_API_DEBUG=false
      
      # 其他环境变量
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    volumes:
      # 挂载数据目录
      - ./data:/app/data
      - ./docs:/app/docs
      # 挂载配置文件
      - ./.env:/app/.env:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/api/v1/bug_recall/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - bug-recall-network

networks:
  bug-recall-network:
    driver: bridge
